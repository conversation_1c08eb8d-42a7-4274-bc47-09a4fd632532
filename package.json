{"name": "eko", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18", "react-dom": "^18", "firebase": "^10.10.0", "recharts": "^2.12.7", "lucide-react": "^0.460.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "tailwindcss": "^3.4.1", "autoprefixer": "^10.0.1", "postcss": "^8"}}