{"name": "eko", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tailwindcss/postcss": "^4.1.12", "clsx": "^2.1.1", "firebase": "^10.10.0", "lucide-react": "^0.460.0", "next": "^15.5.1", "react": "^18", "react-dom": "^18", "recharts": "^2.12.7", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}