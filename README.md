# 🎵 Eko - AI Music Discovery Platform

Eko is an AI-powered music discovery platform that combines Shazam-style song detection, intelligent similarity recommendations, and fun visual music analytics.

## 🚀 Current Status: Week 1 & 2 Complete ✅

### Week 1 Features (MVP Foundation) ✅
- ✅ React + Next.js app setup with Vercel deployment ready
- ✅ Firebase project integration (Firestore + Storage + Auth)
- ✅ Audio recording from microphone using Web Audio API
- ✅ Audio upload to Firebase Storage with metadata storage
- ✅ Clean, responsive UI with Tailwind CSS

### Week 2 Features (Song Recognition) ✅
- ✅ Song recognition using Audd.io API
- ✅ Detected song display with rich metadata
- ✅ Song history storage in Firestore
- ✅ Streaming platform links (Spotify, Apple Music)
- ✅ Test functionality for API verification

### Coming Next (Week 3+)
- 🔄 Similar song recommendations using Spotify API
- 🔄 Playlist building and export features
- 🔄 Song history browsing and search
- 🔄 Advanced audio analysis and visualizations

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd eko
npm install
```

### 2. Firebase Setup

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable the following services:
   - **Firestore Database** (in production mode)
   - **Storage** (with default rules)
   - **Authentication** (optional for now)

3. Get your Firebase configuration:
   - Go to Project Settings > General > Your apps
   - Click "Web app" and register your app
   - Copy the configuration object

4. Create environment variables:
   ```bash
   cp .env.local.example .env.local
   ```

5. Fill in your Firebase credentials in `.env.local`:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   NEXT_PUBLIC_AUDD_API_TOKEN=your_audd_api_token_here
   ```

### 3. Song Recognition API Setup

1. Sign up for a free Audd.io account at [https://dashboard.audd.io/](https://dashboard.audd.io/)
2. Get your API token from the dashboard
3. Add it to your `.env.local` file as `NEXT_PUBLIC_AUDD_API_TOKEN`

### 4. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the app.

### 5. Test the Application

1. **Test API Setup**: Use the "Test Song Recognition" button to verify your Audd.io API setup
2. **Test Audio Recording**:
   - Allow microphone permissions when prompted
   - Click "Start Recording" and record 10-30 seconds of a song
   - Click "Stop Recording"
   - Watch as Eko identifies the song and displays detailed information
3. **Check Firebase**: Verify that audio files and song data are stored in your Firebase project

## 🏗️ Project Structure

```
src/
├── app/
│   ├── layout.tsx              # App layout with Eko branding
│   ├── page.tsx                # Main page with complete workflow
│   └── globals.css             # Global styles
├── components/
│   ├── AudioRecorder.tsx       # Audio recording component
│   ├── SongCard.tsx           # Song display component
│   └── TestSongRecognition.tsx # API testing component
└── lib/
    ├── firebase.ts             # Firebase configuration
    ├── audioUpload.ts          # Audio upload utilities
    ├── songRecognition.ts      # Audd.io API integration
    └── songStorage.ts          # Firestore song storage
```

## 🔧 Technologies Used

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Firebase (Firestore, Storage, Auth)
- **Audio**: Web Audio API, MediaRecorder API
- **Icons**: Lucide React
- **Deployment**: Vercel (ready)

## 📋 Next Steps (Week 3+)

1. **Similar Song Recommendations**
   - Integrate with Spotify API for audio features
   - Implement similarity algorithm using audio features
   - Display recommended songs based on identified tracks

2. **Enhanced User Experience**
   - Song history browsing and search
   - Playlist creation and management
   - Export playlists to Spotify/Apple Music

3. **Advanced Features**
   - Audio analysis and visualizations
   - Genre classification and mood detection
   - Social features and sharing

## 🚀 Deployment

The app is ready for deployment on Vercel:

```bash
npm run build
```

Or deploy directly:
```bash
vercel --prod
```

Make sure to add your environment variables in the Vercel dashboard.

## 🤝 Contributing

This is a learning project following the roadmap. Feel free to suggest improvements or report issues!

## 📄 License

MIT License - feel free to use this project for learning and development.
