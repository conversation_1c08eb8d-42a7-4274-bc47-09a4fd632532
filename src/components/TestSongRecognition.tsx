'use client';

import { useState } from 'react';
import { Play, Loader2, Check<PERSON>ircle, AlertCircle } from 'lucide-react';
import { testSongRecognition, recognizeSongFromUrl } from '@/lib/songRecognition';
import SongCard from './SongCard';

export default function TestSongRecognition() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    setIsLoading(true);
    setError(null);
    setTestResult(null);

    try {
      console.log('Testing song recognition with sample audio...');
      
      // Test with Audd.io's example audio file
      const result = await recognizeSongFromUrl('https://audd.tech/example.mp3');
      
      if (result) {
        setTestResult(result);
        console.log('Test successful:', result);
      } else {
        setError('Test failed: No song recognized from sample audio');
      }
      
    } catch (err) {
      console.error('Test failed:', err);
      setError(err instanceof Error ? err.message : 'Test failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">🧪 Test Song Recognition</h3>
      
      <div className="space-y-4">
        <p className="text-gray-600 text-sm">
          Test the song recognition functionality with a sample audio file to verify your API setup.
        </p>
        
        <button
          onClick={runTest}
          disabled={isLoading}
          className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Testing...</span>
            </>
          ) : (
            <>
              <Play className="w-4 h-4" />
              <span>Run Test</span>
            </>
          )}
        </button>

        {/* Test Results */}
        {testResult && (
          <div className="space-y-4">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
              <div className="flex items-center mb-2">
                <CheckCircle className="w-5 h-5 mr-2" />
                <span className="font-medium">Test Successful!</span>
              </div>
              <p className="text-sm">Song recognition is working correctly.</p>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-800 mb-2">Recognized Song:</h4>
              <SongCard song={testResult} />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
            <div className="flex items-center mb-2">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span className="font-medium">Test Failed</span>
            </div>
            <p className="text-sm">{error}</p>
            <div className="mt-2 text-xs">
              <p><strong>Common issues:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>Missing or invalid NEXT_PUBLIC_AUDD_API_TOKEN in .env.local</li>
                <li>Network connectivity issues</li>
                <li>API rate limits exceeded</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
