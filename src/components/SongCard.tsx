'use client';

import { ExternalLink, Music, Clock, Calendar, Tag } from 'lucide-react';
import { SongRecognitionResult } from '@/lib/songRecognition';

interface SongCardProps {
  song: SongRecognitionResult;
  className?: string;
}

export default function SongCard({ song, className = '' }: SongCardProps) {
  // Get the best available artwork
  const getArtwork = () => {
    if (song.spotify?.album?.images?.[0]) {
      return song.spotify.album.images[0].url;
    }
    if (song.apple_music?.artwork?.url) {
      return song.apple_music.artwork.url.replace('{w}x{h}', '300x300');
    }
    return null;
  };

  const artwork = getArtwork();

  // Format duration
  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get streaming links
  const getStreamingLinks = () => {
    const links = [];
    
    if (song.spotify?.external_urls?.spotify) {
      links.push({
        name: 'Spotify',
        url: song.spotify.external_urls.spotify,
        color: 'bg-green-600 hover:bg-green-700'
      });
    }
    
    if (song.apple_music?.url) {
      links.push({
        name: 'Apple Music',
        url: song.apple_music.url,
        color: 'bg-gray-800 hover:bg-gray-900'
      });
    }
    
    if (song.song_link) {
      links.push({
        name: 'More Platforms',
        url: song.song_link,
        color: 'bg-blue-600 hover:bg-blue-700'
      });
    }
    
    return links;
  };

  const streamingLinks = getStreamingLinks();

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      <div className="flex flex-col md:flex-row">
        {/* Artwork */}
        <div className="md:w-48 md:h-48 w-full h-48 bg-gray-200 flex items-center justify-center">
          {artwork ? (
            <img
              src={artwork}
              alt={`${song.title} by ${song.artist}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <Music className="w-16 h-16 text-gray-400" />
          )}
        </div>

        {/* Song Information */}
        <div className="flex-1 p-6">
          <div className="mb-4">
            <h2 className="text-2xl font-bold text-gray-800 mb-1">{song.title}</h2>
            <p className="text-xl text-gray-600 mb-2">{song.artist}</p>
            <p className="text-lg text-gray-500">{song.album}</p>
          </div>

          {/* Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {song.release_date && (
              <div className="flex items-center text-gray-600">
                <Calendar className="w-4 h-4 mr-2" />
                <span className="text-sm">Released: {song.release_date}</span>
              </div>
            )}
            
            {song.label && (
              <div className="flex items-center text-gray-600">
                <Tag className="w-4 h-4 mr-2" />
                <span className="text-sm">Label: {song.label}</span>
              </div>
            )}
            
            {song.spotify?.duration_ms && (
              <div className="flex items-center text-gray-600">
                <Clock className="w-4 h-4 mr-2" />
                <span className="text-sm">Duration: {formatDuration(song.spotify.duration_ms)}</span>
              </div>
            )}
            
            {song.timecode && (
              <div className="flex items-center text-gray-600">
                <Clock className="w-4 h-4 mr-2" />
                <span className="text-sm">Match at: {song.timecode}</span>
              </div>
            )}
          </div>

          {/* Genres (if available) */}
          {song.apple_music?.genreNames && song.apple_music.genreNames.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {song.apple_music.genreNames.map((genre, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                  >
                    {genre}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Popularity (if available) */}
          {song.spotify?.popularity && (
            <div className="mb-4">
              <div className="flex items-center">
                <span className="text-sm text-gray-600 mr-2">Popularity:</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-32">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${song.spotify.popularity}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-600 ml-2">{song.spotify.popularity}/100</span>
              </div>
            </div>
          )}

          {/* Streaming Links */}
          {streamingLinks.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {streamingLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`inline-flex items-center px-4 py-2 text-white text-sm font-medium rounded-lg transition-colors ${link.color}`}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  {link.name}
                </a>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
