'use client';

import { useState } from 'react';
import { Music, CheckCircle, AlertCircle, Search } from 'lucide-react';
import AudioRecorder from '@/components/AudioRecorder';
import SongCard from '@/components/SongCard';
import TestSongRecognition from '@/components/TestSongRecognition';
import { uploadAudioToFirebase, AudioUploadResult } from '@/lib/audioUpload';
import { recognizeSong, SongRecognitionResult } from '@/lib/songRecognition';
import { storeSongRecognition } from '@/lib/songStorage';

export default function Home() {
  const [isUploading, setIsUploading] = useState(false);
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [uploadResult, setUploadResult] = useState<AudioUploadResult | null>(null);
  const [recognizedSong, setRecognizedSong] = useState<SongRecognitionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleAudioRecorded = async (audioBlob: Blob) => {
    setIsUploading(true);
    setIsRecognizing(false);
    setError(null);
    setUploadResult(null);
    setRecognizedSong(null);

    try {
      // Step 1: Upload to Firebase
      console.log('Audio recorded, uploading to Firebase...');
      const uploadResult = await uploadAudioToFirebase(audioBlob);
      setUploadResult(uploadResult);
      console.log('Upload successful:', uploadResult);

      // Step 2: Recognize the song
      setIsUploading(false);
      setIsRecognizing(true);
      console.log('Starting song recognition...');

      const songResult = await recognizeSong(audioBlob);

      if (songResult) {
        setRecognizedSong(songResult);
        console.log('Song recognized:', songResult);

        // Step 3: Store the recognized song in Firestore
        try {
          await storeSongRecognition(songResult, uploadResult.id);
          console.log('Song stored in history');
        } catch (storeError) {
          console.error('Failed to store song in history:', storeError);
          // Don't show this error to user as the main functionality worked
        }
      } else {
        setError('No song match found. Try recording a clearer audio snippet or a different part of the song.');
      }

    } catch (err) {
      console.error('Process failed:', err);
      setError(err instanceof Error ? err.message : 'Process failed');
    } finally {
      setIsUploading(false);
      setIsRecognizing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Music className="w-12 h-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-800">Eko</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            AI-powered music discovery platform. Record audio snippets to identify songs and discover similar tracks.
          </p>
        </header>

        {/* Main Content */}
        <main className="max-w-6xl mx-auto">
          {/* Recognized Song Display */}
          {recognizedSong && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">🎵 Song Identified!</h2>
              <SongCard song={recognizedSong} />
            </div>
          )}

          <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-2">
            {/* Audio Recorder */}
            <div className="flex justify-center">
              <AudioRecorder
                onAudioRecorded={handleAudioRecorded}
                isUploading={isUploading || isRecognizing}
              />
            </div>

            {/* Status Panel */}
            <div className="space-y-4">
              {/* Recognition Status */}
              {isRecognizing && (
                <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Search className="w-5 h-5 mr-2 animate-spin" />
                    <span className="font-medium">Recognizing Song...</span>
                  </div>
                  <p className="text-sm">Analyzing audio and searching our music database...</p>
                </div>
              )}

              {/* Upload Status */}
              {uploadResult && !isRecognizing && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                  <div className="flex items-center mb-2">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Audio Uploaded Successfully!</span>
                  </div>
                  <div className="text-sm space-y-1">
                    <p><strong>File:</strong> {uploadResult.fileName}</p>
                    <p><strong>Size:</strong> {(uploadResult.size / 1024).toFixed(1)} KB</p>
                  </div>
                </div>
              )}

              {/* Error Display */}
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                  <div className="flex items-center mb-2">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Error</span>
                  </div>
                  <p className="text-sm">{error}</p>
                </div>
              )}

              {/* Instructions */}
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">How to Use Eko</h3>
                <ol className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">1</span>
                    <span>Click "Start Recording" to begin capturing audio</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">2</span>
                    <span>Record 10-30 seconds of clear audio from the song</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">3</span>
                    <span>Click "Stop Recording" when finished</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5">4</span>
                    <span>Eko will identify the song and show detailed information</span>
                  </li>
                </ol>
              </div>

              {/* Features */}
              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">✅ Current Features:</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Audio recording from microphone</li>
                  <li>• Song recognition and identification</li>
                  <li>• Song metadata and streaming links</li>
                  <li>• Firebase storage and history</li>
                </ul>
              </div>

              {/* Test Component */}
              <TestSongRecognition />

              {/* Coming Soon */}
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">🚧 Coming Soon:</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Similar song recommendations</li>
                  <li>• Playlist building features</li>
                  <li>• Song history and favorites</li>
                  <li>• Advanced audio analysis</li>
                </ul>
              </div>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="text-center mt-16 text-gray-500">
          <p>&copy; 2024 Eko - AI Music Discovery Platform</p>
        </footer>
      </div>
    </div>
  );
}
