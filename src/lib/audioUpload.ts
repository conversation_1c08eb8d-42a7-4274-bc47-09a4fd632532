import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { storage, db } from './firebase';

export interface AudioUploadResult {
  id: string;
  downloadURL: string;
  fileName: string;
  size: number;
  uploadedAt: Date;
}

export interface AudioMetadata {
  fileName: string;
  size: number;
  duration?: number;
  mimeType: string;
  uploadedAt: any; // Firestore timestamp
  downloadURL: string;
  userId?: string; // For future user authentication
}

/**
 * Uploads an audio blob to Firebase Storage and stores metadata in Firestore
 */
export async function uploadAudioToFirebase(
  audioBlob: Blob,
  fileName?: string
): Promise<AudioUploadResult> {
  try {
    // Generate a unique filename if not provided
    const timestamp = Date.now();
    const finalFileName = fileName || `audio_${timestamp}.webm`;
    
    // Create a reference to the file in Firebase Storage
    const storageRef = ref(storage, `audio-snippets/${finalFileName}`);
    
    // Upload the file
    console.log('Uploading audio to Firebase Storage...');
    const snapshot = await uploadBytes(storageRef, audioBlob);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    // Prepare metadata for Firestore
    const metadata: AudioMetadata = {
      fileName: finalFileName,
      size: audioBlob.size,
      mimeType: audioBlob.type,
      uploadedAt: serverTimestamp(),
      downloadURL,
      // duration can be added later when we implement audio analysis
    };
    
    // Store metadata in Firestore
    console.log('Storing metadata in Firestore...');
    const docRef = await addDoc(collection(db, 'audioSnippets'), metadata);
    
    console.log('Audio uploaded successfully:', docRef.id);
    
    return {
      id: docRef.id,
      downloadURL,
      fileName: finalFileName,
      size: audioBlob.size,
      uploadedAt: new Date(),
    };
    
  } catch (error) {
    console.error('Error uploading audio:', error);
    throw new Error(`Failed to upload audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Converts audio blob to base64 for API calls (useful for song recognition APIs)
 */
export function audioToBase64(audioBlob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(audioBlob);
  });
}

/**
 * Gets the duration of an audio blob (useful for metadata)
 */
export function getAudioDuration(audioBlob: Blob): Promise<number> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(audioBlob);
    
    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url);
      resolve(audio.duration);
    };
    
    audio.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load audio metadata'));
    };
    
    audio.src = url;
  });
}
