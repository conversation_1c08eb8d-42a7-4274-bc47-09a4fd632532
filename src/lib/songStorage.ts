import { collection, addDoc, serverTimestamp, query, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from './firebase';
import { SongRecognitionResult } from './songRecognition';

export interface StoredSong {
  id: string;
  songData: SongRecognitionResult;
  audioSnippetId?: string; // Reference to the audio snippet in Firebase Storage
  recognizedAt: any; // Firestore timestamp
  userId?: string; // For future user authentication
}

export interface SongHistoryItem extends StoredSong {
  recognizedAt: Date; // Converted from Firestore timestamp
}

/**
 * Stores a recognized song in Firestore
 */
export async function storeSongRecognition(
  songData: SongRecognitionResult,
  audioSnippetId?: string,
  userId?: string
): Promise<string> {
  try {
    const songRecord = {
      songData,
      audioSnippetId,
      userId,
      recognizedAt: serverTimestamp(),
    };

    console.log('Storing song recognition in Firestore...');
    const docRef = await addDoc(collection(db, 'recognizedSongs'), songRecord);
    
    console.log('Song stored successfully with ID:', docRef.id);
    return docRef.id;
    
  } catch (error) {
    console.error('Error storing song recognition:', error);
    throw new Error(`Failed to store song: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Retrieves recent song recognition history
 */
export async function getSongHistory(limitCount: number = 10): Promise<SongHistoryItem[]> {
  try {
    const q = query(
      collection(db, 'recognizedSongs'),
      orderBy('recognizedAt', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const songs: SongHistoryItem[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      songs.push({
        id: doc.id,
        songData: data.songData,
        audioSnippetId: data.audioSnippetId,
        userId: data.userId,
        recognizedAt: data.recognizedAt?.toDate() || new Date(),
      });
    });
    
    return songs;
    
  } catch (error) {
    console.error('Error fetching song history:', error);
    throw new Error(`Failed to fetch song history: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Gets song statistics
 */
export async function getSongStats(): Promise<{
  totalSongs: number;
  uniqueArtists: Set<string>;
  topGenres: Map<string, number>;
}> {
  try {
    const querySnapshot = await getDocs(collection(db, 'recognizedSongs'));
    
    const uniqueArtists = new Set<string>();
    const genreCounts = new Map<string, number>();
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const songData = data.songData as SongRecognitionResult;
      
      // Track unique artists
      if (songData.artist) {
        uniqueArtists.add(songData.artist);
      }
      
      // Track genres from Apple Music data
      if (songData.apple_music?.genreNames) {
        songData.apple_music.genreNames.forEach(genre => {
          genreCounts.set(genre, (genreCounts.get(genre) || 0) + 1);
        });
      }
    });
    
    return {
      totalSongs: querySnapshot.size,
      uniqueArtists,
      topGenres: genreCounts,
    };
    
  } catch (error) {
    console.error('Error fetching song stats:', error);
    throw new Error(`Failed to fetch song stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Searches for songs by artist or title
 */
export async function searchSongs(searchTerm: string): Promise<SongHistoryItem[]> {
  try {
    // Note: This is a simple client-side search. For production, you'd want to use
    // Firestore's full-text search capabilities or a dedicated search service like Algolia
    const querySnapshot = await getDocs(collection(db, 'recognizedSongs'));
    const songs: SongHistoryItem[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const songData = data.songData as SongRecognitionResult;
      
      // Simple case-insensitive search
      const searchLower = searchTerm.toLowerCase();
      const artistMatch = songData.artist?.toLowerCase().includes(searchLower);
      const titleMatch = songData.title?.toLowerCase().includes(searchLower);
      const albumMatch = songData.album?.toLowerCase().includes(searchLower);
      
      if (artistMatch || titleMatch || albumMatch) {
        songs.push({
          id: doc.id,
          songData: data.songData,
          audioSnippetId: data.audioSnippetId,
          userId: data.userId,
          recognizedAt: data.recognizedAt?.toDate() || new Date(),
        });
      }
    });
    
    // Sort by recognition date (most recent first)
    songs.sort((a, b) => b.recognizedAt.getTime() - a.recognizedAt.getTime());
    
    return songs;
    
  } catch (error) {
    console.error('Error searching songs:', error);
    throw new Error(`Failed to search songs: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
