// Song recognition service using Audd.io API

export interface SongRecognitionResult {
  artist: string;
  title: string;
  album: string;
  release_date: string;
  label: string;
  timecode: string;
  song_link: string;
  apple_music?: {
    url: string;
    artwork: {
      url: string;
      width: number;
      height: number;
    };
    durationInMillis: number;
    genreNames: string[];
  };
  spotify?: {
    id: string;
    external_urls: {
      spotify: string;
    };
    album: {
      name: string;
      images: Array<{
        url: string;
        height: number;
        width: number;
      }>;
    };
    artists: Array<{
      name: string;
      external_urls: {
        spotify: string;
      };
    }>;
    popularity: number;
    duration_ms: number;
  };
}

export interface AuddApiResponse {
  status: 'success' | 'error';
  result?: SongRecognitionResult;
  error?: {
    error_code: number;
    error_message: string;
  };
}

/**
 * Recognizes a song from an audio blob using Audd.io API
 */
export async function recognizeSong(audioBlob: Blob): Promise<SongRecognitionResult | null> {
  const apiToken = "20d2346b377a733076a518286f27228b";
  
  if (!apiToken) {
    throw new Error('Audd.io API token not configured. Please add NEXT_PUBLIC_AUDD_API_TOKEN to your environment variables.');
  }

  try {
    // Create FormData for the API request
    const formData = new FormData();
    formData.append('api_token', apiToken);
    formData.append('file', audioBlob, 'audio.webm');
    formData.append('return', 'apple_music,spotify'); // Request additional metadata

    console.log('Sending audio to Audd.io for recognition...');
    
    const response = await fetch('https://api.audd.io/', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AuddApiResponse = await response.json();
    
    console.log('Audd.io API response:', data);

    if (data.status === 'error') {
      console.error('Audd.io API error:', data.error);
      throw new Error(`API Error ${data.error?.error_code}: ${data.error?.error_message}`);
    }

    if (data.status === 'success' && data.result) {
      console.log('Song recognized successfully:', data.result);
      return data.result;
    }

    // No match found
    console.log('No song match found');
    return null;

  } catch (error) {
    console.error('Error recognizing song:', error);
    throw error;
  }
}

/**
 * Recognizes a song from an audio URL using Audd.io API
 */
export async function recognizeSongFromUrl(audioUrl: string): Promise<SongRecognitionResult | null> {
  const apiToken = process.env.NEXT_PUBLIC_AUDD_API_TOKEN;
  
  if (!apiToken) {
    throw new Error('Audd.io API token not configured. Please add NEXT_PUBLIC_AUDD_API_TOKEN to your environment variables.');
  }

  try {
    const formData = new FormData();
    formData.append('api_token', apiToken);
    formData.append('url', audioUrl);
    formData.append('return', 'apple_music,spotify');

    console.log('Sending audio URL to Audd.io for recognition...');
    
    const response = await fetch('https://api.audd.io/', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: AuddApiResponse = await response.json();
    
    console.log('Audd.io API response:', data);

    if (data.status === 'error') {
      console.error('Audd.io API error:', data.error);
      throw new Error(`API Error ${data.error?.error_code}: ${data.error?.error_message}`);
    }

    if (data.status === 'success' && data.result) {
      console.log('Song recognized successfully:', data.result);
      return data.result;
    }

    return null;

  } catch (error) {
    console.error('Error recognizing song from URL:', error);
    throw error;
  }
}

/**
 * Test function to verify API connectivity with a known audio sample
 */
export async function testSongRecognition(): Promise<boolean> {
  try {
    // Use Audd.io's test audio file
    const result = await recognizeSongFromUrl('https://audd.tech/example.mp3');
    return result !== null;
  } catch (error) {
    console.error('Song recognition test failed:', error);
    return false;
  }
}
